import React, { useState } from 'react';
import {
  View,
  Text,
  StyleSheet,
  TouchableOpacity,
  ScrollView,
  Alert,
} from 'react-native';
import { Play, CheckCircle, XCircle, Clock, RefreshCw } from 'lucide-react-native';
import { useLikesStore } from '@/stores/likesStore';
import { likesService } from '@/services/likesService';
import { likesCacheService } from '@/services/likesCache';
import { likesNotificationService } from '@/services/likesNotifications';
import { getRealTimeLikesManager } from '@/services/realTimeLikes';
import { theme } from '@/constants/theme';

interface TestResult {
  name: string;
  status: 'pending' | 'running' | 'passed' | 'failed';
  message?: string;
  duration?: number;
}

export default function LikesTestSuite() {
  const [testResults, setTestResults] = useState<TestResult[]>([]);
  const [isRunning, setIsRunning] = useState(false);
  const likesStore = useLikesStore();

  const updateTestResult = (name: string, status: TestResult['status'], message?: string, duration?: number) => {
    setTestResults(prev => prev.map(test => 
      test.name === name ? { ...test, status, message, duration } : test
    ));
  };

  const runTest = async (testName: string, testFn: () => Promise<void>) => {
    const startTime = Date.now();
    updateTestResult(testName, 'running');
    
    try {
      await testFn();
      const duration = Date.now() - startTime;
      updateTestResult(testName, 'passed', 'Test passed successfully', duration);
    } catch (error) {
      const duration = Date.now() - startTime;
      updateTestResult(testName, 'failed', error instanceof Error ? error.message : 'Unknown error', duration);
    }
  };

  const initializeTests = () => {
    const tests: TestResult[] = [
      { name: 'Likes Store Initialization', status: 'pending' },
      { name: 'Fetch Received Likes', status: 'pending' },
      { name: 'Send Like', status: 'pending' },
      { name: 'Like Back', status: 'pending' },
      { name: 'Send Pass', status: 'pending' },
      { name: 'Cache Service', status: 'pending' },
      { name: 'Notification Service', status: 'pending' },
      { name: 'Real-time Manager', status: 'pending' },
      { name: 'Offline Support', status: 'pending' },
      { name: 'Error Handling', status: 'pending' },
    ];
    setTestResults(tests);
  };

  const runAllTests = async () => {
    setIsRunning(true);
    initializeTests();

    // Test 1: Likes Store Initialization
    await runTest('Likes Store Initialization', async () => {
      const state = likesStore.getState();
      if (typeof state.fetchReceivedLikes !== 'function') {
        throw new Error('Store not properly initialized');
      }
    });

    // Test 2: Fetch Received Likes
    await runTest('Fetch Received Likes', async () => {
      await likesStore.getState().fetchReceivedLikes(true);
      const state = likesStore.getState();
      if (state.error) {
        throw new Error(`Fetch failed: ${state.error}`);
      }
    });

    // Test 3: Send Like
    await runTest('Send Like', async () => {
      const result = await likesService.sendLike('test-user-1', 'like');
      if (!result.like || !result.message) {
        throw new Error('Invalid response from sendLike');
      }
    });

    // Test 4: Like Back
    await runTest('Like Back', async () => {
      const result = await likesService.likeBack('test-user-2');
      if (typeof result.isMatch !== 'boolean') {
        throw new Error('Invalid response from likeBack');
      }
    });

    // Test 5: Send Pass
    await runTest('Send Pass', async () => {
      const result = await likesService.sendPass('test-user-3');
      if (!result.message) {
        throw new Error('Invalid response from sendPass');
      }
    });

    // Test 6: Cache Service
    await runTest('Cache Service', async () => {
      const testData = {
        receivedLikes: [],
        sentLikes: [],
        matches: [],
      };
      
      await likesCacheService.cacheLikesData(testData);
      const cachedData = await likesCacheService.getCachedLikesData();
      
      if (!cachedData) {
        throw new Error('Failed to cache or retrieve data');
      }
    });

    // Test 7: Notification Service
    await runTest('Notification Service', async () => {
      await likesNotificationService.initialize();
      if (!likesNotificationService.isReady()) {
        throw new Error('Notification service failed to initialize');
      }
    });

    // Test 8: Real-time Manager
    await runTest('Real-time Manager', async () => {
      const manager = getRealTimeLikesManager('test-user');
      if (!manager) {
        throw new Error('Failed to create real-time manager');
      }
    });

    // Test 9: Offline Support
    await runTest('Offline Support', async () => {
      // Simulate offline state
      likesStore.getState().setOnlineStatus(false);
      
      // Add pending action
      await likesCacheService.addPendingAction({
        type: 'like',
        userId: 'test-offline-user',
      });
      
      const pendingActions = await likesCacheService.getPendingActions();
      if (pendingActions.length === 0) {
        throw new Error('Failed to add pending action');
      }
      
      // Clean up
      await likesCacheService.clearPendingActions();
      likesStore.getState().setOnlineStatus(true);
    });

    // Test 10: Error Handling
    await runTest('Error Handling', async () => {
      // Test error state management
      likesStore.getState().clearError();
      const state = likesStore.getState();
      
      if (state.error !== null) {
        throw new Error('Error state not properly cleared');
      }
    });

    setIsRunning(false);
  };

  const getStatusIcon = (status: TestResult['status']) => {
    switch (status) {
      case 'pending':
        return <Clock size={16} color={theme.colors.gray400} />;
      case 'running':
        return <RefreshCw size={16} color={theme.colors.warning} />;
      case 'passed':
        return <CheckCircle size={16} color={theme.colors.success} />;
      case 'failed':
        return <XCircle size={16} color={theme.colors.error} />;
    }
  };

  const getStatusColor = (status: TestResult['status']) => {
    switch (status) {
      case 'pending':
        return theme.colors.gray400;
      case 'running':
        return theme.colors.warning;
      case 'passed':
        return theme.colors.success;
      case 'failed':
        return theme.colors.error;
    }
  };

  const showTestDetails = (test: TestResult) => {
    Alert.alert(
      test.name,
      `Status: ${test.status}\n${test.message ? `Message: ${test.message}\n` : ''}${test.duration ? `Duration: ${test.duration}ms` : ''}`,
      [{ text: 'OK' }]
    );
  };

  const passedTests = testResults.filter(t => t.status === 'passed').length;
  const failedTests = testResults.filter(t => t.status === 'failed').length;
  const totalTests = testResults.length;

  return (
    <View style={styles.container}>
      <View style={styles.header}>
        <Text style={styles.title}>Likes Test Suite</Text>
        <TouchableOpacity
          style={[styles.runButton, isRunning && styles.runButtonDisabled]}
          onPress={runAllTests}
          disabled={isRunning}
        >
          <Play size={16} color="white" />
          <Text style={styles.runButtonText}>
            {isRunning ? 'Running...' : 'Run Tests'}
          </Text>
        </TouchableOpacity>
      </View>

      {testResults.length > 0 && (
        <View style={styles.summary}>
          <Text style={styles.summaryText}>
            {passedTests}/{totalTests} tests passed
            {failedTests > 0 && ` • ${failedTests} failed`}
          </Text>
        </View>
      )}

      <ScrollView style={styles.testList} showsVerticalScrollIndicator={false}>
        {testResults.map((test, index) => (
          <TouchableOpacity
            key={index}
            style={styles.testItem}
            onPress={() => showTestDetails(test)}
          >
            <View style={styles.testHeader}>
              {getStatusIcon(test.status)}
              <Text style={[styles.testName, { color: getStatusColor(test.status) }]}>
                {test.name}
              </Text>
            </View>
            {test.message && (
              <Text style={styles.testMessage} numberOfLines={2}>
                {test.message}
              </Text>
            )}
            {test.duration && (
              <Text style={styles.testDuration}>{test.duration}ms</Text>
            )}
          </TouchableOpacity>
        ))}
      </ScrollView>
    </View>
  );
}

const styles = StyleSheet.create({
  container: {
    flex: 1,
    backgroundColor: 'white',
    padding: 16,
  },
  header: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
    marginBottom: 16,
  },
  title: {
    fontSize: 20,
    fontWeight: 'bold',
    color: theme.colors.text,
  },
  runButton: {
    flexDirection: 'row',
    alignItems: 'center',
    backgroundColor: theme.colors.primary,
    paddingHorizontal: 16,
    paddingVertical: 8,
    borderRadius: 8,
    gap: 8,
  },
  runButtonDisabled: {
    opacity: 0.6,
  },
  runButtonText: {
    color: 'white',
    fontWeight: '600',
  },
  summary: {
    backgroundColor: theme.colors.gray100,
    padding: 12,
    borderRadius: 8,
    marginBottom: 16,
  },
  summaryText: {
    fontSize: 14,
    color: theme.colors.text,
    textAlign: 'center',
  },
  testList: {
    flex: 1,
  },
  testItem: {
    backgroundColor: 'white',
    borderWidth: 1,
    borderColor: theme.colors.gray200,
    borderRadius: 8,
    padding: 12,
    marginBottom: 8,
  },
  testHeader: {
    flexDirection: 'row',
    alignItems: 'center',
    gap: 8,
    marginBottom: 4,
  },
  testName: {
    fontSize: 16,
    fontWeight: '600',
    flex: 1,
  },
  testMessage: {
    fontSize: 12,
    color: theme.colors.gray600,
    marginLeft: 24,
  },
  testDuration: {
    fontSize: 10,
    color: theme.colors.gray400,
    marginLeft: 24,
    marginTop: 2,
  },
});
