import React, { useState, useEffect } from 'react';
import {
  View,
  Text,
  StyleSheet,
  TouchableOpacity,
  ScrollView,
  Alert,
} from 'react-native';
import { Play, CheckCircle, XCircle, AlertTriangle } from 'lucide-react-native';
import { useLikesStore } from '@/stores/likesStore';
import { useRealTimeLikes } from '@/hooks/useRealTimeLikes';
import { likesService } from '@/services/likesService';
import { likesCacheService } from '@/services/likesCache';
import { likesNotificationService } from '@/services/likesNotifications';
import { theme } from '@/constants/theme';

interface IntegrationTestResult {
  name: string;
  status: 'pending' | 'running' | 'passed' | 'failed' | 'warning';
  message: string;
  details?: string;
}

export default function LikesIntegrationTest() {
  const [testResults, setTestResults] = useState<IntegrationTestResult[]>([]);
  const [isRunning, setIsRunning] = useState(false);
  const likesStore = useLikesStore();
  const { isConnected } = useRealTimeLikes();

  const updateTestResult = (
    name: string, 
    status: IntegrationTestResult['status'], 
    message: string, 
    details?: string
  ) => {
    setTestResults(prev => prev.map(test => 
      test.name === name ? { ...test, status, message, details } : test
    ));
  };

  const runIntegrationTests = async () => {
    setIsRunning(true);
    
    const tests: IntegrationTestResult[] = [
      { name: 'Store Initialization', status: 'pending', message: '' },
      { name: 'Real-time Connection', status: 'pending', message: '' },
      { name: 'Cache Service', status: 'pending', message: '' },
      { name: 'Notification Service', status: 'pending', message: '' },
      { name: 'End-to-End Like Flow', status: 'pending', message: '' },
      { name: 'Offline Support', status: 'pending', message: '' },
      { name: 'Error Recovery', status: 'pending', message: '' },
    ];
    
    setTestResults(tests);

    // Test 1: Store Initialization
    updateTestResult('Store Initialization', 'running', 'Checking store state...');
    try {
      const state = likesStore.getState();
      if (typeof state.fetchReceivedLikes === 'function' && 
          typeof state.sendLike === 'function' &&
          typeof state.likeBack === 'function') {
        updateTestResult('Store Initialization', 'passed', 'Store properly initialized with all methods');
      } else {
        updateTestResult('Store Initialization', 'failed', 'Store missing required methods');
      }
    } catch (error) {
      updateTestResult('Store Initialization', 'failed', `Store initialization failed: ${error}`);
    }

    // Test 2: Real-time Connection
    updateTestResult('Real-time Connection', 'running', 'Checking real-time connection...');
    try {
      const connected = isConnected();
      if (connected) {
        updateTestResult('Real-time Connection', 'passed', 'Real-time connection established');
      } else {
        updateTestResult('Real-time Connection', 'warning', 'Real-time connection not established', 
          'This might be expected in development environment');
      }
    } catch (error) {
      updateTestResult('Real-time Connection', 'failed', `Connection check failed: ${error}`);
    }

    // Test 3: Cache Service
    updateTestResult('Cache Service', 'running', 'Testing cache operations...');
    try {
      const testData = {
        receivedLikes: [],
        sentLikes: [],
        matches: [],
      };
      
      await likesCacheService.cacheLikesData(testData);
      const cachedData = await likesCacheService.getCachedLikesData();
      
      if (cachedData) {
        updateTestResult('Cache Service', 'passed', 'Cache operations working correctly');
      } else {
        updateTestResult('Cache Service', 'failed', 'Failed to cache or retrieve data');
      }
    } catch (error) {
      updateTestResult('Cache Service', 'failed', `Cache service error: ${error}`);
    }

    // Test 4: Notification Service
    updateTestResult('Notification Service', 'running', 'Testing notification service...');
    try {
      await likesNotificationService.initialize();
      const isReady = likesNotificationService.isReady();
      
      if (isReady) {
        updateTestResult('Notification Service', 'passed', 'Notification service initialized successfully');
      } else {
        updateTestResult('Notification Service', 'warning', 'Notification service not ready', 
          'Permissions might not be granted');
      }
    } catch (error) {
      updateTestResult('Notification Service', 'failed', `Notification service error: ${error}`);
    }

    // Test 5: End-to-End Like Flow
    updateTestResult('End-to-End Like Flow', 'running', 'Testing complete like flow...');
    try {
      // Test sending a like
      const likeResult = await likesService.sendLike('test-user-integration', 'like');
      
      if (likeResult.like && likeResult.message) {
        // Test like back
        const likeBackResult = await likesService.likeBack('test-user-integration');
        
        if (typeof likeBackResult.isMatch === 'boolean') {
          updateTestResult('End-to-End Like Flow', 'passed', 
            `Like flow completed successfully. Match: ${likeBackResult.isMatch}`);
        } else {
          updateTestResult('End-to-End Like Flow', 'failed', 'Like back failed');
        }
      } else {
        updateTestResult('End-to-End Like Flow', 'failed', 'Send like failed');
      }
    } catch (error) {
      updateTestResult('End-to-End Like Flow', 'failed', `Like flow error: ${error}`);
    }

    // Test 6: Offline Support
    updateTestResult('Offline Support', 'running', 'Testing offline functionality...');
    try {
      // Simulate offline state
      likesStore.getState().setOnlineStatus(false);
      
      // Add pending action
      await likesCacheService.addPendingAction({
        type: 'like',
        userId: 'test-offline-user',
      });
      
      const pendingActions = await likesCacheService.getPendingActions();
      
      if (pendingActions.length > 0) {
        // Clean up
        await likesCacheService.clearPendingActions();
        likesStore.getState().setOnlineStatus(true);
        
        updateTestResult('Offline Support', 'passed', 'Offline support working correctly');
      } else {
        updateTestResult('Offline Support', 'failed', 'Failed to add pending action');
      }
    } catch (error) {
      updateTestResult('Offline Support', 'failed', `Offline support error: ${error}`);
    }

    // Test 7: Error Recovery
    updateTestResult('Error Recovery', 'running', 'Testing error handling...');
    try {
      // Test error state management
      likesStore.getState().clearError();
      const state = likesStore.getState();
      
      if (state.error === null) {
        updateTestResult('Error Recovery', 'passed', 'Error handling working correctly');
      } else {
        updateTestResult('Error Recovery', 'failed', 'Error state not properly managed');
      }
    } catch (error) {
      updateTestResult('Error Recovery', 'failed', `Error recovery test failed: ${error}`);
    }

    setIsRunning(false);
  };

  const getStatusIcon = (status: IntegrationTestResult['status']) => {
    switch (status) {
      case 'pending':
        return <View style={styles.pendingIcon} />;
      case 'running':
        return <View style={styles.runningIcon} />;
      case 'passed':
        return <CheckCircle size={20} color={theme.colors.success} />;
      case 'failed':
        return <XCircle size={20} color={theme.colors.error} />;
      case 'warning':
        return <AlertTriangle size={20} color={theme.colors.warning} />;
    }
  };

  const showTestDetails = (test: IntegrationTestResult) => {
    Alert.alert(
      test.name,
      `Status: ${test.status}\nMessage: ${test.message}${test.details ? `\n\nDetails: ${test.details}` : ''}`,
      [{ text: 'OK' }]
    );
  };

  const getOverallStatus = () => {
    const failed = testResults.filter(t => t.status === 'failed').length;
    const passed = testResults.filter(t => t.status === 'passed').length;
    const warnings = testResults.filter(t => t.status === 'warning').length;
    
    if (failed > 0) return 'Some tests failed';
    if (warnings > 0) return 'All tests passed with warnings';
    if (passed === testResults.length && testResults.length > 0) return 'All tests passed';
    return 'Ready to run tests';
  };

  return (
    <View style={styles.container}>
      <View style={styles.header}>
        <Text style={styles.title}>Likes Integration Test</Text>
        <TouchableOpacity
          style={[styles.runButton, isRunning && styles.runButtonDisabled]}
          onPress={runIntegrationTests}
          disabled={isRunning}
        >
          <Play size={16} color="white" />
          <Text style={styles.runButtonText}>
            {isRunning ? 'Running...' : 'Run Tests'}
          </Text>
        </TouchableOpacity>
      </View>

      <View style={styles.statusBar}>
        <Text style={styles.statusText}>{getOverallStatus()}</Text>
      </View>

      <ScrollView style={styles.testList} showsVerticalScrollIndicator={false}>
        {testResults.map((test, index) => (
          <TouchableOpacity
            key={index}
            style={styles.testItem}
            onPress={() => showTestDetails(test)}
          >
            <View style={styles.testHeader}>
              {getStatusIcon(test.status)}
              <Text style={styles.testName}>{test.name}</Text>
            </View>
            <Text style={styles.testMessage} numberOfLines={2}>
              {test.message}
            </Text>
          </TouchableOpacity>
        ))}
      </ScrollView>
    </View>
  );
}

const styles = StyleSheet.create({
  container: {
    flex: 1,
    backgroundColor: 'white',
    padding: 16,
  },
  header: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
    marginBottom: 16,
  },
  title: {
    fontSize: 20,
    fontWeight: 'bold',
    color: theme.colors.text,
  },
  runButton: {
    flexDirection: 'row',
    alignItems: 'center',
    backgroundColor: theme.colors.primary,
    paddingHorizontal: 16,
    paddingVertical: 8,
    borderRadius: 8,
    gap: 8,
  },
  runButtonDisabled: {
    opacity: 0.6,
  },
  runButtonText: {
    color: 'white',
    fontWeight: '600',
  },
  statusBar: {
    backgroundColor: theme.colors.gray100,
    padding: 12,
    borderRadius: 8,
    marginBottom: 16,
  },
  statusText: {
    fontSize: 14,
    color: theme.colors.text,
    textAlign: 'center',
    fontWeight: '500',
  },
  testList: {
    flex: 1,
  },
  testItem: {
    backgroundColor: 'white',
    borderWidth: 1,
    borderColor: theme.colors.gray200,
    borderRadius: 8,
    padding: 12,
    marginBottom: 8,
  },
  testHeader: {
    flexDirection: 'row',
    alignItems: 'center',
    gap: 12,
    marginBottom: 4,
  },
  testName: {
    fontSize: 16,
    fontWeight: '600',
    color: theme.colors.text,
    flex: 1,
  },
  testMessage: {
    fontSize: 14,
    color: theme.colors.gray600,
    marginLeft: 32,
  },
  pendingIcon: {
    width: 20,
    height: 20,
    borderRadius: 10,
    backgroundColor: theme.colors.gray300,
  },
  runningIcon: {
    width: 20,
    height: 20,
    borderRadius: 10,
    backgroundColor: theme.colors.warning,
  },
});
