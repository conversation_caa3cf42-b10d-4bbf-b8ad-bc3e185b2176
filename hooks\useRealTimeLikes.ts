import { useEffect, useRef } from 'react';
import { AppState, AppStateStatus, NetInfo } from 'react-native';
import { useLikesStore } from '@/stores/likesStore';
import { getRealTimeLikesManager, disconnectRealTimeLikes } from '@/services/realTimeLikes';
import { likesNotificationService } from '@/services/likesNotifications';
import { useAuth } from '@/contexts/AuthContext';

export function useRealTimeLikes() {
  const { user } = useAuth();
  const realTimeManagerRef = useRef<any>(null);
  const { setOnlineStatus, loadFromCache } = useLikesStore();

  useEffect(() => {
    if (!user?.id) return;

    // Initialize services
    const initializeServices = async () => {
      try {
        // Initialize notification service
        await likesNotificationService.initialize();
        
        // Initialize real-time manager
        realTimeManagerRef.current = getRealTimeLikesManager(user.id);
        
        // Load cached data on startup
        await loadFromCache();
        
        console.log('Real-time likes services initialized');
      } catch (error) {
        console.error('Failed to initialize real-time likes services:', error);
      }
    };

    initializeServices();

    // Cleanup on unmount
    return () => {
      if (realTimeManagerRef.current) {
        disconnectRealTimeLikes();
        realTimeManagerRef.current = null;
      }
    };
  }, [user?.id, loadFromCache]);

  // Handle network connectivity changes
  useEffect(() => {
    const unsubscribe = NetInfo.addEventListener(state => {
      const isOnline = state.isConnected && state.isInternetReachable;
      console.log('Network status changed:', isOnline ? 'online' : 'offline');
      setOnlineStatus(isOnline ?? false);
    });

    return unsubscribe;
  }, [setOnlineStatus]);

  // Handle app state changes
  useEffect(() => {
    const handleAppStateChange = (nextAppState: AppStateStatus) => {
      if (nextAppState === 'active') {
        // App came to foreground
        console.log('App became active, checking connection');
        
        // Check network status
        NetInfo.fetch().then(state => {
          const isOnline = state.isConnected && state.isInternetReachable;
          setOnlineStatus(isOnline ?? false);
        });

        // Clear notification badge
        likesNotificationService.clearBadge();
      } else if (nextAppState === 'background') {
        // App went to background
        console.log('App went to background');
      }
    };

    const subscription = AppState.addEventListener('change', handleAppStateChange);

    return () => {
      subscription?.remove();
    };
  }, [setOnlineStatus]);

  // Return utility functions
  return {
    isConnected: () => realTimeManagerRef.current?.isConnected() ?? false,
    disconnect: () => {
      if (realTimeManagerRef.current) {
        disconnectRealTimeLikes();
        realTimeManagerRef.current = null;
      }
    },
  };
}

// Hook for managing notification preferences
export function useNotificationPreferences() {
  const getPreferences = async () => {
    return await likesNotificationService.getNotificationPreferences();
  };

  const setPreference = async (type: string, enabled: boolean) => {
    await likesNotificationService.setNotificationPreference(type, enabled);
  };

  return {
    getPreferences,
    setPreference,
  };
}
