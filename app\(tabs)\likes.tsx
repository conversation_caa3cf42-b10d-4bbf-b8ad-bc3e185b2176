import React, { useState, useEffect } from 'react';
import {
  View,
  Text,
  StyleSheet,
  SafeAreaView,
  TouchableOpacity,
  ScrollView,
  RefreshControl,
  Alert,
  Dimensions,
} from 'react-native';
import { LinearGradient } from 'expo-linear-gradient';
import { Heart, MessageCircle, Crown, AlertCircle, Wifi, WifiOff } from 'lucide-react-native';
import { useRouter } from 'expo-router';
import { useLikesStore } from '@/stores/likesStore';
import AnimatedLikeCard from '@/components/likes/AnimatedLikeCard';
import AnimatedMatchCard from '@/components/likes/AnimatedMatchCard';
import { LikesGridSkeleton, MatchesListSkeleton } from '@/components/ui/LikesSkeleton';
import LikesServiceTest from '@/components/debug/LikesServiceTest';
import DateHandlingTest from '@/components/debug/DateHandlingTest';
import { useRealTimeLikes } from '@/hooks/useRealTimeLikes';
import { theme } from '@/constants/theme';

const { width } = Dimensions.get('window');
const CARD_WIDTH = (width - 48) / 2;

export default function LikesTab() {
  const router = useRouter();
  const [activeTab, setActiveTab] = useState<'likes' | 'matches'>('likes');

  const {
    receivedLikes,
    matches,
    isLoading,
    isRefreshing,
    error,
    isOnline,
    hasMoreLikes,
    hasMoreMatches,
    fetchReceivedLikes,
    fetchMatches,
    likeBack,
    clearError,
  } = useLikesStore();

  // Initialize real-time likes functionality
  const { isConnected } = useRealTimeLikes();

  useEffect(() => {
    let isMounted = true;

    // Initial data fetch
    const initializeData = async () => {
      try {
        if (isMounted && receivedLikes.length === 0) {
          await fetchReceivedLikes();
        }
        if (isMounted && matches.length === 0) {
          await fetchMatches();
        }
      } catch (error) {
        console.error('Error initializing likes data:', error);
      }
    };

    initializeData();

    return () => {
      isMounted = false;
    };
  }, [fetchReceivedLikes, fetchMatches, receivedLikes.length, matches.length]);

  const handleRefresh = async () => {
    if (activeTab === 'likes') {
      await fetchReceivedLikes(true);
    } else {
      await fetchMatches(true);
    }
  };

  const handleLoadMore = () => {
    if (activeTab === 'likes' && hasMoreLikes && !isLoading) {
      fetchReceivedLikes();
    } else if (activeTab === 'matches' && hasMoreMatches && !isLoading) {
      fetchMatches();
    }
  };

  const handleLikeBack = async (userId: string) => {
    try {
      const isMatch = await likeBack(userId);
      if (isMatch) {
        // Switch to matches tab to show the new match
        setTimeout(() => setActiveTab('matches'), 2000);
      }
      return isMatch;
    } catch (error) {
      Alert.alert('Error', 'Failed to like back. Please try again.');
      return false;
    }
  };

  const handleMessage = (userId: string) => {
    router.push(`/chat/${userId}`);
  };

  const handleMatchPress = (matchId: string) => {
    // Find the match and navigate to chat
    const match = matches.find(m => m.id === matchId);
    if (match) {
      const otherUserId = match.users.find(id => id !== 'current-user');
      if (otherUserId) {
        router.push(`/chat/${otherUserId}`);
      }
    }
  };

  const handleProfilePress = (profile: any) => {
    // Navigate to profile detail view
    console.log('View profile:', profile.id);
  };

  const renderError = () => {
    if (!error) return null;

    return (
      <View style={styles.errorContainer}>
        <AlertCircle size={24} color={theme.colors.error} />
        <Text style={styles.errorText}>{error}</Text>
        <TouchableOpacity style={styles.retryButton} onPress={clearError}>
          <Text style={styles.retryButtonText}>Retry</Text>
        </TouchableOpacity>
      </View>
    );
  };

  const renderEmptyState = () => {
    if (activeTab === 'likes') {
      return (
        <View style={styles.emptyState}>
          <Heart size={64} color="rgba(255, 255, 255, 0.5)" />
          <Text style={styles.emptyTitle}>No likes yet</Text>
          <Text style={styles.emptySubtitle}>
            Keep swiping to find people who like you!
          </Text>
        </View>
      );
    } else {
      return (
        <View style={styles.emptyState}>
          <MessageCircle size={64} color="rgba(255, 255, 255, 0.5)" />
          <Text style={styles.emptyTitle}>No matches yet</Text>
          <Text style={styles.emptySubtitle}>
            Start swiping to find your perfect match!
          </Text>
        </View>
      );
    }
  };

  return (
    <LinearGradient colors={['#8B5CF6', '#EC4899']} style={styles.container}>
      <SafeAreaView style={styles.safeArea}>
        <View style={styles.header}>
          <Text style={styles.title}>Likes & Matches</Text>
          {!isOnline && (
            <View style={styles.offlineIndicator}>
              <WifiOff size={16} color="white" />
              <Text style={styles.offlineText}>Offline</Text>
            </View>
          )}
        </View>

        <View style={styles.tabBar}>
          <TouchableOpacity
            style={[styles.tab, activeTab === 'likes' && styles.activeTab]}
            onPress={() => setActiveTab('likes')}
          >
            <Heart
              size={20}
              color={activeTab === 'likes' ? '#8B5CF6' : 'rgba(255, 255, 255, 0.7)'}
            />
            <Text
              style={[
                styles.tabText,
                activeTab === 'likes' && styles.activeTabText,
              ]}
            >
              Likes ({receivedLikes.length})
            </Text>
          </TouchableOpacity>

          <TouchableOpacity
            style={[styles.tab, activeTab === 'matches' && styles.activeTab]}
            onPress={() => setActiveTab('matches')}
          >
            <MessageCircle
              size={20}
              color={activeTab === 'matches' ? '#8B5CF6' : 'rgba(255, 255, 255, 0.7)'}
            />
            <Text
              style={[
                styles.tabText,
                activeTab === 'matches' && styles.activeTabText,
              ]}
            >
              Matches ({matches.length})
            </Text>
          </TouchableOpacity>
        </View>

        <ScrollView
          style={styles.content}
          showsVerticalScrollIndicator={false}
          refreshControl={
            <RefreshControl
              refreshing={isRefreshing}
              onRefresh={handleRefresh}
              tintColor="white"
              colors={['white']}
            />
          }
          onMomentumScrollEnd={(event) => {
            const { layoutMeasurement, contentOffset, contentSize } = event.nativeEvent;
            const isCloseToBottom = layoutMeasurement.height + contentOffset.y >= contentSize.height - 20;
            if (isCloseToBottom) {
              handleLoadMore();
            }
          }}
        >
          {error && renderError()}

          {/* Debug Components - Remove in production */}
          {__DEV__ && (
            <>
              <LikesServiceTest />
              <DateHandlingTest />
            </>
          )}

          {activeTab === 'likes' ? (
            <View style={styles.likesContainer}>
              <View style={styles.premiumBanner}>
                <Crown size={24} color="#FFD700" />
                <View style={styles.premiumBannerContent}>
                  <Text style={styles.premiumBannerTitle}>See Who Likes You</Text>
                  <Text style={styles.premiumBannerSubtitle}>
                    Upgrade to Premium to see all your likes instantly
                  </Text>
                </View>
                <TouchableOpacity style={styles.upgradeButton}>
                  <Text style={styles.upgradeButtonText}>Upgrade</Text>
                </TouchableOpacity>
              </View>

              <View style={styles.likesGrid}>
                {receivedLikes.map((profile) => (
                  <AnimatedLikeCard
                    key={profile.id}
                    profile={profile}
                    onLikeBack={handleLikeBack}
                    onMessage={handleMessage}
                    onPress={handleProfilePress}
                  />
                ))}

                {isLoading && !isRefreshing && (
                  <LikesGridSkeleton count={4} />
                )}
              </View>

              {receivedLikes.length === 0 && !isLoading && renderEmptyState()}
            </View>
          ) : (
            <View style={styles.matchesContainer}>
              {matches.map((match) => (
                <AnimatedMatchCard
                  key={match.id}
                  match={match}
                  onPress={handleMatchPress}
                />
              ))}

              {isLoading && !isRefreshing && (
                <MatchesListSkeleton count={3} />
              )}

              {matches.length === 0 && !isLoading && renderEmptyState()}
            </View>
          )}
        </ScrollView>
      </SafeAreaView>
    </LinearGradient>
  );
}

const styles = StyleSheet.create({
  container: {
    flex: 1,
  },
  safeArea: {
    flex: 1,
  },
  header: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
    paddingHorizontal: 24,
    paddingVertical: 16,
  },
  title: {
    fontSize: 28,
    fontFamily: 'Poppins-Bold',
    color: 'white',
  },
  offlineIndicator: {
    flexDirection: 'row',
    alignItems: 'center',
    backgroundColor: 'rgba(255, 255, 255, 0.2)',
    paddingHorizontal: 12,
    paddingVertical: 6,
    borderRadius: 16,
    gap: 6,
  },
  offlineText: {
    fontSize: 12,
    fontFamily: 'Inter-Medium',
    color: 'white',
  },
  tabBar: {
    flexDirection: 'row',
    backgroundColor: 'rgba(255, 255, 255, 0.1)',
    marginHorizontal: 24,
    borderRadius: 12,
    padding: 4,
    marginBottom: 24,
  },
  tab: {
    flex: 1,
    flexDirection: 'row',
    alignItems: 'center',
    justifyContent: 'center',
    paddingVertical: 12,
    borderRadius: 8,
    gap: 8,
  },
  activeTab: {
    backgroundColor: 'white',
  },
  tabText: {
    fontSize: 14,
    fontFamily: 'Inter-Medium',
    color: 'rgba(255, 255, 255, 0.7)',
  },
  activeTabText: {
    color: '#8B5CF6',
  },
  content: {
    flex: 1,
  },
  likesContainer: {
    paddingHorizontal: 24,
  },
  premiumBanner: {
    flexDirection: 'row',
    alignItems: 'center',
    backgroundColor: 'rgba(255, 255, 255, 0.1)',
    borderRadius: 12,
    padding: 16,
    marginBottom: 24,
  },
  premiumBannerContent: {
    flex: 1,
    marginLeft: 12,
  },
  premiumBannerTitle: {
    fontSize: 16,
    fontFamily: 'Inter-SemiBold',
    color: 'white',
    marginBottom: 4,
  },
  premiumBannerSubtitle: {
    fontSize: 14,
    fontFamily: 'Inter-Regular',
    color: 'rgba(255, 255, 255, 0.8)',
  },
  upgradeButton: {
    backgroundColor: '#FFD700',
    borderRadius: 8,
    paddingHorizontal: 16,
    paddingVertical: 8,
  },
  upgradeButtonText: {
    fontSize: 14,
    fontFamily: 'Inter-SemiBold',
    color: '#000',
  },
  likesGrid: {
    flexDirection: 'row',
    flexWrap: 'wrap',
    gap: 16,
    paddingBottom: 24,
  },
  likeCard: {
    width: CARD_WIDTH,
    height: CARD_WIDTH * 1.3,
    borderRadius: 12,
    overflow: 'hidden',
    backgroundColor: 'white',
    shadowColor: '#000',
    shadowOffset: { width: 0, height: 4 },
    shadowOpacity: 0.1,
    shadowRadius: 8,
    elevation: 4,
  },
  likeCardImage: {
    width: '100%',
    height: '100%',
    position: 'absolute',
  },
  premiumBadge: {
    position: 'absolute',
    top: 8,
    right: 8,
    backgroundColor: 'rgba(0, 0, 0, 0.7)',
    borderRadius: 12,
    padding: 6,
    zIndex: 1,
  },
  likeCardGradient: {
    position: 'absolute',
    bottom: 0,
    left: 0,
    right: 0,
    height: '40%',
  },
  likeCardInfo: {
    position: 'absolute',
    bottom: 0,
    left: 0,
    right: 0,
    padding: 12,
  },
  likeCardName: {
    fontSize: 16,
    fontFamily: 'Inter-SemiBold',
    color: 'white',
    marginBottom: 8,
  },
  matchButton: {
    flexDirection: 'row',
    alignItems: 'center',
    justifyContent: 'center',
    backgroundColor: '#42DDA6',
    borderRadius: 8,
    paddingVertical: 8,
    gap: 6,
  },
  matchButtonText: {
    fontSize: 12,
    fontFamily: 'Inter-SemiBold',
    color: 'white',
  },
  likeBackButton: {
    flexDirection: 'row',
    alignItems: 'center',
    justifyContent: 'center',
    backgroundColor: 'white',
    borderRadius: 8,
    paddingVertical: 8,
    gap: 6,
  },
  likeBackButtonText: {
    fontSize: 12,
    fontFamily: 'Inter-SemiBold',
    color: '#42DDA6',
  },
  matchesContainer: {
    paddingHorizontal: 24,
    paddingBottom: 24,
  },
  matchCard: {
    flexDirection: 'row',
    alignItems: 'center',
    backgroundColor: 'rgba(255, 255, 255, 0.1)',
    borderRadius: 12,
    padding: 16,
    marginBottom: 12,
    position: 'relative',
  },
  matchPhoto: {
    width: 60,
    height: 60,
    borderRadius: 30,
    marginRight: 16,
  },
  matchInfo: {
    flex: 1,
  },
  matchHeader: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
    marginBottom: 4,
  },
  matchName: {
    fontSize: 16,
    fontFamily: 'Inter-SemiBold',
    color: 'white',
  },
  matchTime: {
    fontSize: 12,
    fontFamily: 'Inter-Regular',
    color: 'rgba(255, 255, 255, 0.6)',
  },
  matchMessage: {
    fontSize: 14,
    fontFamily: 'Inter-Regular',
    color: 'rgba(255, 255, 255, 0.8)',
  },
  unreadMessage: {
    fontFamily: 'Inter-SemiBold',
    color: 'white',
  },
  unreadDot: {
    width: 8,
    height: 8,
    borderRadius: 4,
    backgroundColor: '#42DDA6',
    position: 'absolute',
    right: 16,
  },
  emptyState: {
    alignItems: 'center',
    paddingVertical: 60,
  },
  emptyTitle: {
    fontSize: 20,
    fontFamily: 'Poppins-SemiBold',
    color: 'white',
    marginTop: 16,
    marginBottom: 8,
  },
  emptySubtitle: {
    fontSize: 16,
    fontFamily: 'Inter-Regular',
    color: 'rgba(255, 255, 255, 0.7)',
    textAlign: 'center',
  },
  errorContainer: {
    alignItems: 'center',
    paddingVertical: 20,
    paddingHorizontal: 16,
    marginHorizontal: 16,
    marginBottom: 16,
    backgroundColor: 'rgba(239, 68, 68, 0.1)',
    borderRadius: 12,
    borderWidth: 1,
    borderColor: 'rgba(239, 68, 68, 0.3)',
  },
  errorText: {
    fontSize: 14,
    fontFamily: 'Inter-Regular',
    color: theme.colors.error,
    textAlign: 'center',
    marginTop: 8,
    marginBottom: 12,
  },
  retryButton: {
    backgroundColor: theme.colors.error,
    paddingHorizontal: 16,
    paddingVertical: 8,
    borderRadius: 8,
  },
  retryButtonText: {
    fontSize: 14,
    fontFamily: 'Inter-SemiBold',
    color: 'white',
  },
});