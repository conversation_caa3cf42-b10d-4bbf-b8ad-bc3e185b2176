import { create } from 'zustand';
import { persist, createJSONStorage } from 'zustand/middleware';
import AsyncStorage from '@react-native-async-storage/async-storage';
import { Like, Match, LikeProfile } from '@/types/messaging';
import { likesService } from '@/services/likesService';
import { safeParseDate } from '@/utils/dateUtils';

interface LikesState {
  // Data
  receivedLikes: LikeProfile[];
  sentLikes: Like[];
  matches: Match[];
  
  // UI State
  isLoading: boolean;
  isRefreshing: boolean;
  error: string | null;
  
  // Pagination
  hasMoreLikes: boolean;
  hasMoreMatches: boolean;
  likesPage: number;
  matchesPage: number;
  
  // Actions
  fetchReceivedLikes: (refresh?: boolean) => Promise<void>;
  fetchMatches: (refresh?: boolean) => Promise<void>;
  sendLike: (userId: string, type: 'like' | 'superlike') => Promise<boolean>;
  sendPass: (userId: string) => Promise<void>;
  likeBack: (userId: string) => Promise<boolean>;
  
  // Real-time updates
  addNewLike: (like: LikeProfile) => void;
  addNewMatch: (match: Match) => void;
  updateMatchStatus: (matchId: string, status: Match['status']) => void;
  
  // Utility
  clearError: () => void;
  reset: () => void;
}

const initialState = {
  receivedLikes: [],
  sentLikes: [],
  matches: [],
  isLoading: false,
  isRefreshing: false,
  error: null,
  hasMoreLikes: true,
  hasMoreMatches: true,
  likesPage: 1,
  matchesPage: 1,
};

/**
 * Normalizes dates in LikeProfile objects to handle serialization issues
 */
function normalizeLikeProfile(profile: LikeProfile): LikeProfile {
  return {
    ...profile,
    likedAt: safeParseDate(profile.likedAt) || new Date(),
  };
}

/**
 * Normalizes dates in Match objects to handle serialization issues
 */
function normalizeMatch(match: Match & { otherUser?: any }): Match & { otherUser?: any } {
  return {
    ...match,
    timestamp: safeParseDate(match.timestamp) || new Date(),
    lastActivity: match.lastActivity ? safeParseDate(match.lastActivity) || undefined : undefined,
  };
}

export const useLikesStore = create<LikesState>()(
  persist(
    (set, get) => ({
      ...initialState,

      fetchReceivedLikes: async (refresh = false) => {
        const state = get();

        if (state.isLoading && !refresh) return;

        // Use a try-catch to prevent state updates on unmounted components
        try {
          set({
            isLoading: !refresh,
            isRefreshing: refresh,
            error: null
          });
        } catch (error) {
          console.warn('Failed to update loading state:', error);
          return;
        }

        try {
          const page = refresh ? 1 : state.likesPage;
          const data = await likesService.fetchReceivedLikes(page, 20);

          try {
            // Normalize dates in the received data
            const normalizedLikes = data.likes.map(normalizeLikeProfile);

            set((state) => ({
              receivedLikes: refresh
                ? normalizedLikes
                : [...state.receivedLikes, ...normalizedLikes],
              hasMoreLikes: data.hasMore,
              likesPage: refresh ? 2 : state.likesPage + 1,
              isLoading: false,
              isRefreshing: false,
            }));
          } catch (setError) {
            console.warn('Failed to update likes state:', setError);
          }
        } catch (error) {
          try {
            set({
              error: error instanceof Error ? error.message : 'Unknown error',
              isLoading: false,
              isRefreshing: false,
            });
          } catch (setError) {
            console.warn('Failed to update error state:', setError);
          }
        }
      },

      fetchMatches: async (refresh = false) => {
        const state = get();
        
        if (state.isLoading && !refresh) return;
        
        set({ 
          isLoading: !refresh, 
          isRefreshing: refresh,
          error: null 
        });

        try {
          const page = refresh ? 1 : state.matchesPage;
          const data = await likesService.fetchMatches(page, 20);

          // Normalize dates in the received data
          const normalizedMatches = data.matches.map(normalizeMatch);

          set((state) => ({
            matches: refresh
              ? normalizedMatches
              : [...state.matches, ...normalizedMatches],
            hasMoreMatches: data.hasMore,
            matchesPage: refresh ? 2 : state.matchesPage + 1,
            isLoading: false,
            isRefreshing: false,
          }));
        } catch (error) {
          set({
            error: error instanceof Error ? error.message : 'Unknown error',
            isLoading: false,
            isRefreshing: false,
          });
        }
      },

      sendLike: async (userId: string, type: 'like' | 'superlike') => {
        try {
          const data = await likesService.sendLike(userId, type);

          // Add to sent likes
          set((state) => ({
            sentLikes: [...state.sentLikes, data.like],
          }));

          // Return whether it's a match
          return data.isMatch;
        } catch (error) {
          set({ error: error instanceof Error ? error.message : 'Unknown error' });
          return false;
        }
      },

      sendPass: async (userId: string) => {
        try {
          await likesService.sendPass(userId);
        } catch (error) {
          set({ error: error instanceof Error ? error.message : 'Unknown error' });
        }
      },

      likeBack: async (userId: string) => {
        try {
          const data = await likesService.likeBack(userId);

          // Update the like to show it's now a match
          set((state) => ({
            receivedLikes: state.receivedLikes.map(like =>
              like.id === userId ? { ...like, isMatch: true } : like
            ),
          }));

          // Add to matches if it's a new match
          if (data.isMatch && data.match) {
            set((state) => ({
              matches: [data.match, ...state.matches],
            }));
          }

          return data.isMatch;
        } catch (error) {
          set({ error: error instanceof Error ? error.message : 'Unknown error' });
          return false;
        }
      },

      addNewLike: (like: LikeProfile) => {
        set((state) => ({
          receivedLikes: [like, ...state.receivedLikes],
        }));
      },

      addNewMatch: (match: Match) => {
        set((state) => ({
          matches: [match, ...state.matches],
        }));
      },

      updateMatchStatus: (matchId: string, status: Match['status']) => {
        set((state) => ({
          matches: state.matches.map(match =>
            match.id === matchId ? { ...match, status } : match
          ),
        }));
      },

      clearError: () => set({ error: null }),
      
      reset: () => set(initialState),
    }),
    {
      name: 'likes-storage',
      storage: createJSONStorage(() => AsyncStorage),
      partialize: (state) => ({
        receivedLikes: state.receivedLikes,
        sentLikes: state.sentLikes,
        matches: state.matches,
      }),
      onRehydrateStorage: () => (state) => {
        // Normalize dates when rehydrating from storage
        if (state) {
          state.receivedLikes = state.receivedLikes.map(normalizeLikeProfile);
          state.matches = state.matches.map(normalizeMatch);
        }
      },
    }
  )
);
